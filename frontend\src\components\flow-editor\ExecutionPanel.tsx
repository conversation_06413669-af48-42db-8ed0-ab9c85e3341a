import { useState, useEffect, useRef } from 'react'
import { FlowExecution, ExecutionLog } from '@rpa-project/shared'
import { executionApi } from '../../services/api'

interface ExecutionPanelProps {
  isOpen: boolean
  onClose: () => void
  execution: FlowExecution | null
}

export function ExecutionPanel({ isOpen, onClose, execution }: ExecutionPanelProps) {
  const [logs, setLogs] = useState<ExecutionLog[]>([])
  const [loading, setLoading] = useState(false)
  const [cancelling, setCancelling] = useState(false)
  const logsEndRef = useRef<HTMLDivElement>(null)

  // Auto-scroll to bottom when new logs arrive
  const scrollToBottom = () => {
    logsEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [logs])

  // Poll for logs when execution is running
  useEffect(() => {
    if (!execution || !isOpen) {
      setLogs([])
      return
    }

    const loadLogs = async () => {
      try {
        setLoading(true)
        const response = await executionApi.getExecutionLogs(execution.id)
        if (response.success) {
          setLogs(response.data || [])
        }
      } catch (error) {
        console.error('Failed to load logs:', error)
      } finally {
        setLoading(false)
      }
    }

    // Load initial logs
    loadLogs()

    // Poll for updates if execution is still running
    const interval = setInterval(() => {
      if (execution.status === 'running' || execution.status === 'pending') {
        loadLogs()
      }
    }, 1000) // Poll every second

    return () => clearInterval(interval)
  }, [execution, isOpen])

  const handleCancel = async () => {
    if (!execution || cancelling) return

    try {
      setCancelling(true)
      await executionApi.cancelExecution(execution.id)
      // The execution status will be updated through polling
    } catch (error) {
      console.error('Failed to cancel execution:', error)
      alert('Failed to cancel execution')
    } finally {
      setCancelling(false)
    }
  }

  const getLogLevelColor = (level: string) => {
    switch (level) {
      case 'error': return '#ef4444'
      case 'warn': return '#f59e0b'
      case 'info': return '#3b82f6'
      case 'debug': return '#6b7280'
      default: return '#6b7280'
    }
  }

  const getLogLevelBadge = (level: string) => {
    switch (level) {
      case 'error': return '❌'
      case 'warn': return '⚠️'
      case 'info': return 'ℹ️'
      case 'debug': return '🔍'
      default: return '📝'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return '#10b981'
      case 'failed': return '#ef4444'
      case 'cancelled': return '#6b7280'
      case 'running': return '#3b82f6'
      case 'pending': return '#f59e0b'
      default: return '#6b7280'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return '✅'
      case 'failed': return '❌'
      case 'cancelled': return '⏹️'
      case 'running': return '🔄'
      case 'pending': return '⏳'
      default: return '❓'
    }
  }

  const formatTimestamp = (timestamp: Date) => {
    return new Date(timestamp).toLocaleTimeString('sv-SE', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  return (
    <>
      {/* Overlay */}
      {isOpen && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.3)',
            zIndex: 1000,
            backdropFilter: 'blur(2px)'
          }}
          onClick={onClose}
        />
      )}

      {/* Panel */}
      <div
        style={{
          position: 'fixed',
          top: 0,
          right: isOpen ? 0 : '-400px',
          width: '400px',
          height: '100vh',
          backgroundColor: 'white',
          boxShadow: '-4px 0 8px rgba(0, 0, 0, 0.1)',
          zIndex: 1001,
          transition: 'right 0.3s ease',
          display: 'flex',
          flexDirection: 'column',
          border: '1px solid #e5e7eb'
        }}
      >
        {/* Header */}
        <div style={{
          padding: '1rem',
          borderBottom: '1px solid #e5e7eb',
          backgroundColor: '#f8fafc'
        }}>
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '0.5rem'
          }}>
            <h3 style={{
              fontSize: '1rem',
              fontWeight: '600',
              color: '#111827',
              margin: 0,
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}>
              🚀 Flow Execution
            </h3>
            <button
              onClick={onClose}
              style={{
                padding: '0.25rem',
                border: 'none',
                backgroundColor: 'transparent',
                cursor: 'pointer',
                fontSize: '1.25rem',
                color: '#6b7280'
              }}
            >
              ✕
            </button>
          </div>

          {execution && (
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.75rem',
              fontSize: '0.875rem'
            }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.25rem',
                color: getStatusColor(execution.status)
              }}>
                <span>{getStatusIcon(execution.status)}</span>
                <span style={{ fontWeight: '500', textTransform: 'capitalize' }}>
                  {execution.status}
                </span>
              </div>
              
              {(execution.status === 'running' || execution.status === 'pending') && (
                <button
                  onClick={handleCancel}
                  disabled={cancelling}
                  style={{
                    padding: '0.25rem 0.5rem',
                    fontSize: '0.75rem',
                    border: '1px solid #ef4444',
                    borderRadius: '0.375rem',
                    backgroundColor: 'white',
                    color: '#ef4444',
                    cursor: cancelling ? 'not-allowed' : 'pointer',
                    opacity: cancelling ? 0.5 : 1
                  }}
                >
                  {cancelling ? '⏳ Cancelling...' : '⏹️ Cancel'}
                </button>
              )}
            </div>
          )}
        </div>

        {/* Logs Content */}
        <div style={{
          flex: 1,
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column'
        }}>
          {!execution ? (
            <div style={{
              flex: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: '#6b7280',
              fontSize: '0.875rem'
            }}>
              No execution running
            </div>
          ) : loading && logs.length === 0 ? (
            <div style={{
              flex: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: '#6b7280',
              fontSize: '0.875rem'
            }}>
              Loading logs...
            </div>
          ) : logs.length === 0 ? (
            <div style={{
              flex: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: '#6b7280',
              fontSize: '0.875rem'
            }}>
              No logs yet
            </div>
          ) : (
            <div style={{
              flex: 1,
              overflowY: 'auto',
              padding: '0.5rem'
            }}>
              {logs.map((log, index) => (
                <div
                  key={index}
                  style={{
                    padding: '0.5rem',
                    marginBottom: '0.25rem',
                    borderRadius: '0.375rem',
                    backgroundColor: '#f9fafb',
                    border: `1px solid ${getLogLevelColor(log.level)}20`,
                    borderLeft: `3px solid ${getLogLevelColor(log.level)}`
                  }}
                >
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    marginBottom: '0.25rem'
                  }}>
                    <span>{getLogLevelBadge(log.level)}</span>
                    <span style={{
                      fontSize: '0.75rem',
                      fontWeight: '500',
                      color: getLogLevelColor(log.level),
                      textTransform: 'uppercase'
                    }}>
                      {log.level}
                    </span>
                    <span style={{
                      fontSize: '0.625rem',
                      color: '#6b7280',
                      fontFamily: 'Monaco, Consolas, "Courier New", monospace'
                    }}>
                      {formatTimestamp(log.timestamp)}
                    </span>
                  </div>
                  <div style={{
                    fontSize: '0.75rem',
                    color: '#374151',
                    lineHeight: '1.4',
                    wordBreak: 'break-word'
                  }}>
                    {log.message}
                  </div>
                  {log.data && (
                    <div style={{
                      marginTop: '0.25rem',
                      fontSize: '0.625rem',
                      color: '#6b7280',
                      fontFamily: 'Monaco, Consolas, "Courier New", monospace',
                      backgroundColor: '#f3f4f6',
                      padding: '0.25rem',
                      borderRadius: '0.25rem',
                      overflow: 'auto'
                    }}>
                      {JSON.stringify(log.data, null, 2)}
                    </div>
                  )}
                </div>
              ))}
              <div ref={logsEndRef} />
            </div>
          )}
        </div>
      </div>
    </>
  )
}

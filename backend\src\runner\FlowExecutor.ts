import { RpaFlow, RpaStep, ExecutionLog } from '@rpa-project/shared';
import { getRunnerFactory, RunnerFactoryConfig } from './RunnerFactory';
import { IRunner, RunnerContext } from './IRunner';
import { getRunnerTypeForStep, isRunnerImplemented, RunnerType } from './stepTypes';

/**
 * Orchestrates the execution of RPA flows using multiple runners
 */
export class FlowExecutor {
  private runnerFactory = getRunnerFactory();
  private executionId: string;
  private logHandler: (log: Omit<ExecutionLog, 'timestamp'>) => void;
  private cancellationChecker?: () => Promise<boolean>;
  private initializedRunners = new Map<string, IRunner>();

  constructor(
    executionId: string,
    logHandler: (log: Omit<ExecutionLog, 'timestamp'>) => void,
    cancellationChecker?: () => Promise<boolean>
  ) {
    this.executionId = executionId;
    this.logHandler = logHandler;
    this.cancellationChecker = cancellationChecker;
  }

  /**
   * Execute an entire RPA flow using appropriate runners for each step
   */
  async executeFlow(flow: RpaFlow, variables: Record<string, any> = {}): Promise<Record<string, any>> {
    this.logHandler({
      level: 'info',
      message: `Starting execution of flow: ${flow.name} (${flow.steps.length} steps)`
    });

    // Analyze the flow to understand what runners are needed
    const analysis = this.runnerFactory.analyzeFlow(flow);
    
    if (analysis.unsupportedSteps.length > 0) {
      const errorMessage = `Flow contains unsupported step types: ${analysis.unsupportedSteps.join(', ')}`;
      this.logHandler({
        level: 'error',
        message: errorMessage
      });
      throw new Error(errorMessage);
    }

    this.logHandler({
      level: 'info',
      message: `Flow requires runners: ${analysis.requiredRunners.join(', ')}`
    });

    const context: RunnerContext = {
      variables: { ...variables },
      onLog: this.logHandler,
      cancellationChecker: this.cancellationChecker
    };

    try {
      // Check if this is a single-runner flow (e.g., all web automation steps)
      if (analysis.requiredRunners.length === 1 && analysis.requiredRunners[0] === RunnerType.PLAYWRIGHT) {
        // Use PlaywrightRunner's own executeFlow method which includes delays
        const runner = this.runnerFactory.getOrCreateRunner(this.executionId, RunnerType.PLAYWRIGHT, {
          logHandler: this.logHandler,
          cancellationChecker: this.cancellationChecker
        });

        this.logHandler({
          level: 'info',
          message: 'Using PlaywrightRunner executeFlow for web automation flow'
        });

        return await runner.executeFlow!(flow, variables);
      }

      // Multi-runner flow - initialize all required runners
      for (const runnerType of analysis.requiredRunners) {
        // Use getOrCreateRunner to ensure proper registration for cleanup
        const runner = this.runnerFactory.getOrCreateRunner(this.executionId, runnerType, {
          logHandler: this.logHandler,
          cancellationChecker: this.cancellationChecker
        });

        await runner.initialize(flow.settings || {}, variables);
        this.initializedRunners.set(runnerType, runner);

        this.logHandler({
          level: 'info',
          message: `Initialized ${runnerType} runner`
        });
      }

      // Execute steps sequentially
      for (let i = 0; i < flow.steps.length; i++) {
        // Check for cancellation before each step
        if (context.cancellationChecker) {
          const isCancelled = await context.cancellationChecker();
          if (isCancelled) {
            this.logHandler({
              level: 'info',
              message: 'Execution was cancelled, stopping flow'
            });
            throw new Error('Execution was cancelled');
          }
        }

        const step = flow.steps[i];
        const runnerType = getRunnerTypeForStep(step.type);
        const runner = this.initializedRunners.get(runnerType);

        if (!runner) {
          throw new Error(`No initialized runner found for step type: ${step.type}`);
        }

        this.logHandler({
          level: 'info',
          message: `Executing step ${i + 1}/${flow.steps.length}: ${step.type}`,
          stepId: step.id
        });

        const result = await runner.executeStep(step, context);
        
        if (!result.success) {
          throw new Error(result.error || `Step execution failed: ${step.type}`);
        }

        // Update context variables with any results from the step
        if (result.variables) {
          context.variables = { ...context.variables, ...result.variables };
        }

        // Note: Step delays are handled by individual runners
        // PlaywrightRunner adds delays between web automation steps
      }

      this.logHandler({
        level: 'info',
        message: `Flow execution completed successfully`
      });

      return context.variables;

    } catch (error) {
      // Re-throw the error, cleanup will be handled by the caller
      throw error;
    }
  }

  /**
   * Execute a single step using the appropriate runner
   */
  async executeStep(step: RpaStep, context: RunnerContext): Promise<void> {
    const runnerType = getRunnerTypeForStep(step.type);
    
    if (!isRunnerImplemented(runnerType)) {
      throw new Error(`Runner type '${runnerType}' is not implemented for step type '${step.type}'`);
    }

    const runner = this.runnerFactory.getRunnerForExecution(this.executionId, step.type, {
      logHandler: this.logHandler,
      cancellationChecker: this.cancellationChecker
    });

    const result = await runner.executeStep(step, context);
    
    if (!result.success) {
      throw new Error(result.error || `Step execution failed: ${step.type}`);
    }

    // Update context variables with any results from the step
    if (result.variables) {
      context.variables = { ...context.variables, ...result.variables };
    }
  }

  /**
   * Clean up all resources for this execution
   */
  async cleanup(): Promise<void> {
    this.logHandler({
      level: 'info',
      message: `Starting cleanup for execution: ${this.executionId}`
    });

    // First, cleanup local runner instances
    const localCleanupPromises = Array.from(this.initializedRunners.values()).map(async (runner, index) => {
      try {
        await runner.cleanup();
        this.logHandler({
          level: 'info',
          message: `Cleaned up local runner instance ${index + 1}/${this.initializedRunners.size}`
        });
      } catch (error) {
        this.logHandler({
          level: 'warn',
          message: `Error cleaning up local runner: ${error instanceof Error ? error.message : 'Unknown error'}`
        });
      }
    });

    await Promise.all(localCleanupPromises);
    this.initializedRunners.clear();

    // Then cleanup registry cached instances
    try {
      await this.runnerFactory.cleanupExecution(this.executionId);
      this.logHandler({
        level: 'info',
        message: 'Cleaned up execution resources from registry'
      });
    } catch (error) {
      this.logHandler({
        level: 'warn',
        message: `Error during registry cleanup: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    }

    this.logHandler({
      level: 'info',
      message: `Cleanup completed for execution: ${this.executionId}`
    });
  }



  /**
   * Get execution statistics
   */
  getStats(): {
    executionId: string;
    factoryStats: any;
  } {
    return {
      executionId: this.executionId,
      factoryStats: this.runnerFactory.getStats()
    };
  }
}

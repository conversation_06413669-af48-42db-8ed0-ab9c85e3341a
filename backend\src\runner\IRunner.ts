import { RpaFlow, RpaStep, FlowSettings, ExecutionLog } from '@rpa-project/shared';

/**
 * Context interface for runner execution
 */
export interface RunnerContext {
  variables: Record<string, any>;
  onLog: (log: Omit<ExecutionLog, 'timestamp'>) => void;
  cancellationChecker?: () => Promise<boolean>;
}

/**
 * Result interface for step execution
 */
export interface StepExecutionResult {
  success: boolean;
  data?: any;
  error?: string;
  variables?: Record<string, any>;
}

/**
 * Abstract interface for all RPA execution engines
 */
export interface IRunner {
  /**
   * Initialize the runner with flow settings and variables
   */
  initialize(settings: FlowSettings, variables: Record<string, any>): Promise<void>;

  /**
   * Execute a single RPA step
   */
  executeStep(step: RpaStep, context: RunnerContext): Promise<StepExecutionResult>;

  /**
   * Execute an entire flow (optional - can use default implementation)
   */
  executeFlow?(flow: RpaFlow, variables: Record<string, any>): Promise<Record<string, any>>;

  /**
   * Set log handler for the runner
   */
  setLogHandler(handler: (log: Omit<ExecutionLog, 'timestamp'>) => void): void;

  /**
   * Set cancellation checker for the runner
   */
  setCancellationChecker(checker: () => Promise<boolean>): void;

  /**
   * Clean up resources
   */
  cleanup(): Promise<void>;

  /**
   * Get supported step types for this runner
   */
  getSupportedStepTypes(): string[];

  /**
   * Check if this runner can handle a specific step type
   */
  canHandleStep(stepType: string): boolean;
}

/**
 * Abstract base class providing common functionality for runners
 */
export abstract class BaseRunner implements IRunner {
  protected logHandler: (log: Omit<ExecutionLog, 'timestamp'>) => void = () => {};
  protected cancellationChecker: (() => Promise<boolean>) | null = null;
  protected context?: RunnerContext;

  abstract initialize(settings: FlowSettings, variables: Record<string, any>): Promise<void>;
  abstract executeStep(step: RpaStep, context: RunnerContext): Promise<StepExecutionResult>;
  abstract cleanup(): Promise<void>;
  abstract getSupportedStepTypes(): string[];

  setLogHandler(handler: (log: Omit<ExecutionLog, 'timestamp'>) => void): void {
    this.logHandler = handler;
  }

  setCancellationChecker(checker: () => Promise<boolean>): void {
    this.cancellationChecker = checker;
  }

  canHandleStep(stepType: string): boolean {
    return this.getSupportedStepTypes().includes(stepType);
  }

  /**
   * Default flow execution implementation
   * Can be overridden by specific runners if needed
   */
  async executeFlow(flow: RpaFlow, variables: Record<string, any> = {}): Promise<Record<string, any>> {
    await this.initialize(flow.settings || {}, variables);

    const context: RunnerContext = {
      variables: { ...variables },
      onLog: this.logHandler,
      cancellationChecker: this.cancellationChecker || undefined
    };

    try {
      for (let i = 0; i < flow.steps.length; i++) {
        // Check for cancellation before each step
        if (context.cancellationChecker) {
          const isCancelled = await context.cancellationChecker();
          if (isCancelled) {
            context.onLog({
              level: 'info',
              message: 'Execution was cancelled, stopping flow'
            });
            throw new Error('Execution was cancelled');
          }
        }

        const step = flow.steps[i];
        
        // Only execute steps this runner can handle
        if (!this.canHandleStep(step.type)) {
          context.onLog({
            level: 'warn',
            message: `Step type '${step.type}' not supported by ${this.constructor.name}`,
            stepId: step.id
          });
          continue;
        }

        const result = await this.executeStep(step, context);
        
        if (!result.success) {
          throw new Error(result.error || `Step execution failed: ${step.type}`);
        }

        // Update context variables with any results from the step
        if (result.variables) {
          context.variables = { ...context.variables, ...result.variables };
        }

        // Note: Step delays are handled by individual runners if needed
        // Web automation runners may add delays between steps
      }

      return context.variables;
    } finally {
      await this.cleanup();
    }
  }

  /**
   * Sleep utility function
   */
  protected sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

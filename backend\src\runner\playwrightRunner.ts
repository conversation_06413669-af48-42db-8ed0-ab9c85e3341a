import { chromium, firefox, webkit, <PERSON><PERSON><PERSON>, <PERSON>, BrowserContext } from 'playwright';
import {
  RpaFlow,
  RpaStep,
  FlowSettings,
  ExecutionLog
} from '@rpa-project/shared';
import { credentialService } from '../services/credentialService';
import { <PERSON><PERSON><PERSON>ner, RunnerContext, StepExecutionResult } from './IRunner';
import { WEB_AUTOMATION_STEPS } from './stepTypes';

export interface PlaywrightRunnerContext {
  browser: Browser;
  context: BrowserContext;
  page: Page;
  variables: Record<string, any>;
  onLog: (log: Omit<ExecutionLog, 'timestamp'>) => void;
}

export class PlaywrightRunner extends BaseRunner {
  private playwrightContext?: PlaywrightRunnerContext;

  async initialize(settings: FlowSettings, variables: Record<string, any> = {}): Promise<void> {
    const browserType = settings.browser || 'chromium';
    const headless = settings.headless !== false;

    let browser: Browser;
    switch (browserType) {
      case 'firefox':
        browser = await firefox.launch({ headless });
        break;
      case 'webkit':
        browser = await webkit.launch({ headless });
        break;
      default:
        browser = await chromium.launch({ headless });
    }

    const contextOptions: any = {};
    
    if (settings.viewport) {
      contextOptions.viewport = settings.viewport;
    }
    
    if (settings.userAgent) {
      contextOptions.userAgent = settings.userAgent;
    }
    
    if (settings.locale) {
      contextOptions.locale = settings.locale;
    }
    
    if (settings.timezone) {
      contextOptions.timezoneId = settings.timezone;
    }

    const context = await browser.newContext(contextOptions);
    const page = await context.newPage();

    this.playwrightContext = {
      browser,
      context,
      page,
      variables: { ...variables },
      onLog: this.logHandler // Use the stored log handler
    };
  }

  getSupportedStepTypes(): string[] {
    return [...WEB_AUTOMATION_STEPS];
  }

  /**
   * Override executeFlow to add web automation specific delays between steps
   */
  async executeFlow(flow: RpaFlow, variables: Record<string, any> = {}): Promise<Record<string, any>> {
    await this.initialize(flow.settings || {}, variables);

    const context: RunnerContext = {
      variables: { ...variables },
      onLog: this.logHandler,
      cancellationChecker: this.cancellationChecker || undefined
    };

    try {
      for (let i = 0; i < flow.steps.length; i++) {
        // Check for cancellation before each step
        if (context.cancellationChecker) {
          const isCancelled = await context.cancellationChecker();
          if (isCancelled) {
            context.onLog({
              level: 'info',
              message: 'Execution was cancelled, stopping flow'
            });
            throw new Error('Execution was cancelled');
          }
        }

        const step = flow.steps[i];

        // Only execute steps this runner can handle
        if (!this.canHandleStep(step.type)) {
          context.onLog({
            level: 'warn',
            message: `Step type '${step.type}' not supported by PlaywrightRunner`,
            stepId: step.id
          });
          continue;
        }

        const result = await this.executeStep(step, context);

        if (!result.success) {
          throw new Error(result.error || `Step execution failed: ${step.type}`);
        }

        // Update context variables with any results from the step
        if (result.variables) {
          context.variables = { ...context.variables, ...result.variables };
        }

        // Add random delay between web automation steps (except after the last step)
        if (i < flow.steps.length - 1) {
          const delay = this.getRandomDelay();
          context.onLog({
            level: 'info',
            message: `Waiting ${delay}ms before next step...`,
            stepId: step.id
          });
          await this.sleep(delay);
        }
      }

      return context.variables;
    } catch (error) {
      // Re-throw the error, cleanup will be handled by the caller
      throw error;
    }
  }

  async executeStep(step: RpaStep, context: RunnerContext): Promise<StepExecutionResult> {
    if (!this.playwrightContext) {
      throw new Error('PlaywrightRunner not initialized');
    }

    const { page, variables, onLog } = this.playwrightContext;
    const timeout = step.timeout || 30000;

    onLog({
      level: 'info',
      message: `Executing step: ${step.type}`,
      stepId: step.id
    });

    try {
      switch (step.type) {
        case 'navigate':
          await page.goto(step.url, { 
            waitUntil: step.waitUntil || 'load',
            timeout 
          });
          onLog({
            level: 'info',
            message: `Navigated to ${step.url}`,
            stepId: step.id
          });
          break;

        case 'goBack':
          await page.goBack({ timeout });
          onLog({
            level: 'info',
            message: 'Navigated back',
            stepId: step.id
          });
          break;

        case 'goForward':
          await page.goForward({ timeout });
          onLog({
            level: 'info',
            message: 'Navigated forward',
            stepId: step.id
          });
          break;

        case 'reload':
          await page.reload({ timeout });
          onLog({
            level: 'info',
            message: 'Page reloaded',
            stepId: step.id
          });
          break;

        case 'click':
          await page.click(step.selector, {
            button: step.button || 'left',
            clickCount: step.clickCount || 1,
            force: step.force,
            timeout
          });
          onLog({
            level: 'info',
            message: `Clicked element: ${step.selector}`,
            stepId: step.id
          });
          break;

        case 'fill':
          await page.fill(step.selector, step.value, {
            force: step.force,
            timeout
          });
          onLog({
            level: 'info',
            message: `Filled element ${step.selector} with: ${step.value}`,
            stepId: step.id
          });
          break;

        case 'type':
          await page.type(step.selector, step.text, {
            delay: step.delay,
            timeout
          });
          onLog({
            level: 'info',
            message: `Typed in element ${step.selector}: ${step.text}`,
            stepId: step.id
          });
          break;

        case 'selectOption':
          const selectOptions: any = {};
          if (step.value) selectOptions.value = step.value;
          if (step.label) selectOptions.label = step.label;
          if (step.index !== undefined) selectOptions.index = step.index;
          
          await page.selectOption(step.selector, selectOptions, { timeout });
          onLog({
            level: 'info',
            message: `Selected option in ${step.selector}`,
            stepId: step.id
          });
          break;

        case 'check':
          await page.check(step.selector, {
            force: step.force,
            timeout
          });
          onLog({
            level: 'info',
            message: `Checked element: ${step.selector}`,
            stepId: step.id
          });
          break;

        case 'uncheck':
          await page.uncheck(step.selector, {
            force: step.force,
            timeout
          });
          onLog({
            level: 'info',
            message: `Unchecked element: ${step.selector}`,
            stepId: step.id
          });
          break;

        case 'waitForSelector':
          await page.waitForSelector(step.selector, {
            state: step.state || 'visible',
            timeout
          });
          onLog({
            level: 'info',
            message: `Waited for selector: ${step.selector}`,
            stepId: step.id
          });
          break;

        case 'waitForTimeout':
          await page.waitForTimeout(step.duration);
          onLog({
            level: 'info',
            message: `Waited for ${step.duration}ms`,
            stepId: step.id
          });
          break;

        case 'waitForUrl':
          await page.waitForURL(step.url, { timeout });
          onLog({
            level: 'info',
            message: `Waited for URL: ${step.url}`,
            stepId: step.id
          });
          break;

        case 'extractText':
          const text = await page.textContent(step.selector, { timeout });
          variables[step.variableName] = text;
          onLog({
            level: 'info',
            message: `Extracted text from ${step.selector}: ${text}`,
            stepId: step.id,
            data: { [step.variableName]: text }
          });
          break;

        case 'extractAttribute':
          const attribute = await page.getAttribute(step.selector, step.attribute, { timeout });
          variables[step.variableName] = attribute;
          onLog({
            level: 'info',
            message: `Extracted attribute ${step.attribute} from ${step.selector}: ${attribute}`,
            stepId: step.id,
            data: { [step.variableName]: attribute }
          });
          break;

        case 'takeScreenshot':
          const screenshotPath = step.path || `screenshot-${Date.now()}.png`;
          await page.screenshot({
            path: screenshotPath,
            fullPage: step.fullPage || false
          });
          onLog({
            level: 'info',
            message: `Screenshot saved: ${screenshotPath}`,
            stepId: step.id,
            data: { screenshotPath }
          });
          break;

        case 'ifElementExists':
          const elementExists = await page.locator(step.selector).count() > 0;
          const stepsToExecute = elementExists ? step.thenSteps : (step.elseSteps || []);

          onLog({
            level: 'info',
            message: `Element ${step.selector} ${elementExists ? 'exists' : 'does not exist'}, executing ${elementExists ? 'then' : 'else'} steps`,
            stepId: step.id
          });

          for (const conditionalStep of stepsToExecute) {
            await this.executeStep(conditionalStep, context);
          }
          break;

        case 'fillPassword':
          const fillPasswordStep = step as any;
          const passwordData = await credentialService.getDecryptedPassword(fillPasswordStep.credentialId);
          if (!passwordData) {
            throw new Error(`Password credential with id ${fillPasswordStep.credentialId} not found`);
          }

          await page.fill(fillPasswordStep.selector, passwordData.password, {
            force: fillPasswordStep.force,
            timeout
          });
          onLog({
            level: 'info',
            message: `Filled password field ${fillPasswordStep.selector} with credential: ${passwordData.username}`,
            stepId: fillPasswordStep.id
          });
          break;

        case 'fill2FA':
          const fill2FAStep = step as any;
          const totpCode = await credentialService.generateTOTPCode(fill2FAStep.credentialId);
          if (!totpCode) {
            throw new Error(`2FA credential with id ${fill2FAStep.credentialId} not found`);
          }

          await page.fill(fill2FAStep.selector, totpCode, {
            force: fill2FAStep.force,
            timeout
          });
          onLog({
            level: 'info',
            message: `Filled 2FA field ${fill2FAStep.selector} with generated TOTP code`,
            stepId: fill2FAStep.id
          });
          break;

        case 'conditionalClick':
          const conditionalClickStep = step as any;
          let shouldClick = false;
          let conditionResult = false;

          try {
            // Check the condition
            switch (conditionalClickStep.condition) {
              case 'exists':
                conditionResult = await page.locator(conditionalClickStep.selector).count() > 0;
                break;
              case 'enabled':
                const enabledElement = page.locator(conditionalClickStep.selector);
                conditionResult = await enabledElement.count() > 0 && await enabledElement.isEnabled();
                break;
              case 'disabled':
                const disabledElement = page.locator(conditionalClickStep.selector);
                conditionResult = await disabledElement.count() > 0 && await disabledElement.isDisabled();
                break;
              default:
                throw new Error(`Unknown condition: ${conditionalClickStep.condition}`);
            }

            // Determine if we should click based on condition result and clickIfTrue setting
            shouldClick = conditionalClickStep.clickIfTrue ? conditionResult : !conditionResult;

            onLog({
              level: 'info',
              message: `Condition '${conditionalClickStep.condition}' for ${conditionalClickStep.selector}: ${conditionResult}. ${shouldClick ? 'Will click' : 'Will not click'}.`,
              stepId: conditionalClickStep.id
            });

            if (shouldClick) {
              await page.click(conditionalClickStep.selector, {
                button: conditionalClickStep.button || 'left',
                force: conditionalClickStep.force,
                timeout
              });
              onLog({
                level: 'info',
                message: `Clicked ${conditionalClickStep.selector} (${conditionalClickStep.button || 'left'} button)`,
                stepId: conditionalClickStep.id
              });
            } else {
              onLog({
                level: 'info',
                message: `Skipped clicking ${conditionalClickStep.selector} due to condition`,
                stepId: conditionalClickStep.id
              });
            }
          } catch (conditionError) {
            const conditionErrorMessage = conditionError instanceof Error ? conditionError.message : 'Unknown error';
            onLog({
              level: 'warn',
              message: `Could not evaluate condition for ${conditionalClickStep.selector}: ${conditionErrorMessage}. Skipping click.`,
              stepId: conditionalClickStep.id
            });
          }
          break;

        default:
          return {
            success: false,
            error: `Unknown step type: ${(step as any).type}`
          };
      }

      return {
        success: true,
        variables: context.variables
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      onLog({
        level: 'error',
        message: `Error executing step ${step.type}: ${errorMessage}`,
        stepId: step.id,
        data: { error: errorMessage }
      });

      return {
        success: false,
        error: errorMessage
      };
    }
  }

  async cleanup(): Promise<void> {
    if (this.playwrightContext) {
      try {
        // Close browser context first
        if (this.playwrightContext.context) {
          await this.playwrightContext.context.close();
        }
      } catch (error) {
        console.error('Error closing browser context:', error);
      }

      try {
        // Close browser instance
        if (this.playwrightContext.browser) {
          await this.playwrightContext.browser.close();
        }
      } catch (error) {
        console.error('Error closing browser:', error);
      }

      this.playwrightContext = undefined;
      console.log('🧹 PlaywrightRunner cleanup completed');
    }
  }

  setLogHandler(onLog: (log: Omit<ExecutionLog, 'timestamp'>) => void): void {
    this.logHandler = onLog;
    if (this.playwrightContext) {
      this.playwrightContext.onLog = onLog;
    }
  }

  /**
   * Generate random delay between web automation steps (1-5 seconds)
   * Change the values below to adjust timing
   */
  private getRandomDelay(): number {
    const minDelay = 1000;  // 1 second - change this value
    const maxDelay = 5000; // 5 seconds - change this value
    return Math.floor(Math.random() * (maxDelay - minDelay)) + minDelay;
  }

  /**
   * Sleep utility function for waitForTimeout steps and delays between steps
   */
  protected sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
